# Mobile API Reports Documentation

## Overview

The Mobile API Reports system provides comprehensive analytics and reporting functionality for both students and staff members. The system generates various types of reports including attendance, grades, behavioral points (BPS), homework analytics, and class-level reports for teachers.

## Base URL Structure

All report endpoints follow the pattern:

```
/mobile-api/reports/{user_type}/{report_type}/
```

## Authentication

All endpoints require authentication via `authCode` parameter:

- **Parameter**: `authCode` (required)
- **Type**: String
- **Description**: Authentication code obtained during login

## Common Response Structure

All report endpoints return data in the following structure:

```json
{
  "success": true,
  "report_type": "report_name",
  "user_info": {
    "id": 123,
    "name": "User Name",
    "user_type": "student|staff"
  },
  "data": {
    "summary": {},
    "chart_data": {},
    "detailed_data": {}
  },
  "generated_at": "2024-01-01T12:00:00.000000Z"
}
```

## Available Reports Endpoint

### GET `/mobile-api/reports/available/`

Get list of available reports for the authenticated user based on their role and permissions.

**Parameters:**

- `authCode` (required): Authentication code

**Response:**

```json
{
  "success": true,
  "user_info": {
    "id": 123,
    "name": "John Doe",
    "user_type": "student"
  },
  "reports": [
    {
      "id": "attendance",
      "name": "My Attendance",
      "description": "View your attendance records and statistics",
      "icon": "calendar-check",
      "category": "academic"
    }
  ]
}
```

## Student Reports

### 1. Student Attendance Report

**Endpoint:** `GET /mobile-api/reports/student/attendance/`

**Parameters:**

- `authCode` (required): Authentication code
- `start_date` (optional): Start date (YYYY-MM-DD format)
- `end_date` (optional): End date (YYYY-MM-DD format)

**Response Structure:**

```json
{
  "success": true,
  "report_type": "student_attendance",
  "student_info": {
    "id": 123,
    "name": "John Doe",
    "branch_id": 1
  },
  "data": {
    "summary": {
      "total_days": 30,
      "present_days": 25,
      "absent_days": 3,
      "late_days": 2,
      "attendance_rate": 83.33
    },
    "chart_data": {
      "labels": ["Present", "Absent", "Late"],
      "datasets": [
        {
          "data": [25, 3, 2],
          "backgroundColor": ["#2ecc71", "#e74c3c", "#f39c12"]
        }
      ],
      "type": "doughnut"
    },
    "monthly_breakdown": [
      {
        "month": "2024-01",
        "present": 20,
        "absent": 2,
        "late": 1,
        "attendance_rate": 86.96
      }
    ]
  }
}
```

### 2. Student Grades Report

**Endpoint:** `GET /mobile-api/reports/student/grades/`

**Parameters:**

- `authCode` (required): Authentication code
- `start_date` (optional): Start date (YYYY-MM-DD format)
- `end_date` (optional): End date (YYYY-MM-DD format)

**Response Structure:**

```json
{
  "data": {
    "summary": {
      "overall_average": 85.5,
      "highest_grade": 95.0,
      "lowest_grade": 72.0,
      "total_subjects": 6,
      "total_assessments": 24
    },
    "subject_breakdown": [
      {
        "subject_id": 1,
        "subject_name": "Mathematics",
        "summative_average": 88.5,
        "formative_average": 82.0,
        "overall_average": 86.5,
        "total_assessments": 4
      }
    ],
    "chart_data": {
      "labels": ["Mathematics", "English", "Science"],
      "datasets": [
        {
          "label": "Overall Average",
          "data": [86.5, 84.2, 87.8],
          "backgroundColor": "#3498db"
        }
      ],
      "type": "bar"
    }
  }
}
```

### 3. Student BPS Report

**Endpoint:** `GET /mobile-api/reports/student/bps/`

**Parameters:**

- `authCode` (required): Authentication code
- `start_date` (optional): Start date (YYYY-MM-DD format)
- `end_date` (optional): End date (YYYY-MM-DD format)

**Response Structure:**

```json
{
  "data": {
    "summary": {
      "total_points": 15,
      "positive_points": 20,
      "negative_points": 5,
      "total_records": 8,
      "net_points": 15
    },
    "chart_data": {
      "labels": ["Positive Points", "Negative Points"],
      "datasets": [
        {
          "data": [20, 5],
          "backgroundColor": ["#2ecc71", "#e74c3c"]
        }
      ],
      "type": "doughnut"
    },
    "top_items": [
      {
        "item_title": "Good Behavior",
        "count": 3,
        "total_points": 15,
        "type": "PRS"
      }
    ]
  }
}
```

### 4. Student Homework Report

**Endpoint:** `GET /mobile-api/reports/student/homework/`

**Parameters:**

- `authCode` (required): Authentication code
- `start_date` (optional): Start date (YYYY-MM-DD format)
- `end_date` (optional): End date (YYYY-MM-DD format)

**Response Structure:**

```json
{
  "data": {
    "summary": {
      "total_homework": 15,
      "completed_homework": 12,
      "pending_homework": 3,
      "completion_rate": 80.0
    },
    "subject_breakdown": [
      {
        "subject_name": "Mathematics",
        "total": 5,
        "completed": 4,
        "pending": 1,
        "completion_rate": 80.0
      }
    ]
  }
}
```

## Staff Reports

### 1. Get Teacher Classes

**Endpoint:** `GET /mobile-api/reports/staff/classes/`

Get list of classes that the teacher can generate reports for.

**Parameters:**

- `authCode` (required): Authentication code

**Response:**

```json
{
  "success": true,
  "staff_info": {
    "id": 456,
    "name": "Jane Smith",
    "role": "subject_teacher",
    "access_level": "class"
  },
  "classes": [
    {
      "classroom_id": 10,
      "classroom_name": "Year 10 Mathematics"
    }
  ],
  "total_classes": 1
}
```

### 2. Class Attendance Report

**Endpoint:** `GET /mobile-api/reports/staff/class-attendance/`

**Parameters:**

- `authCode` (required): Authentication code
- `classroom_id` (required): Classroom ID
- `start_date` (optional): Start date (YYYY-MM-DD format)
- `end_date` (optional): End date (YYYY-MM-DD format)

**Response Structure:**

```json
{
  "data": {
    "summary": {
      "total_students": 25,
      "total_days": 20,
      "present_count": 480,
      "absent_count": 15,
      "late_count": 5,
      "attendance_rate": 96.0
    },
    "daily_breakdown": [
      {
        "date": "2024-01-15",
        "present": 24,
        "absent": 1,
        "late": 0,
        "attendance_rate": 96.0
      }
    ]
  }
}
```

### 3. Class Assessment Report

**Endpoint:** `GET /mobile-api/reports/staff/class-assessment/`

**Parameters:**

- `authCode` (required): Authentication code
- `classroom_id` (required): Classroom ID
- `subject_id` (optional): Subject ID for filtering
- `start_date` (optional): Start date (YYYY-MM-DD format)
- `end_date` (optional): End date (YYYY-MM-DD format)

**Response Structure:**

```json
{
  "data": {
    "summary": {
      "class_average": 78.5,
      "highest_score": 95.0,
      "lowest_score": 45.0,
      "total_students": 25,
      "total_assessments": 8
    },
    "grade_distribution": {
      "A": 5,
      "B": 8,
      "C": 7,
      "D": 3,
      "F": 2
    },
    "chart_data": {
      "labels": [
        "A (90-100)",
        "B (80-89)",
        "C (70-79)",
        "D (60-69)",
        "F (<60)"
      ],
      "datasets": [
        {
          "data": [5, 8, 7, 3, 2],
          "backgroundColor": [
            "#2ecc71",
            "#3498db",
            "#f39c12",
            "#e67e22",
            "#e74c3c"
          ]
        }
      ],
      "type": "doughnut"
    },
    "subject_breakdown": [
      {
        "subject_name": "Mathematics",
        "average": 82.3,
        "total_assessments": 4,
        "students_assessed": 25
      }
    ]
  }
}
```

### 4. Behavioral Analytics Report

**Endpoint:** `GET /mobile-api/reports/staff/behavioral-analytics/`

**Parameters:**

- `authCode` (required): Authentication code
- `classroom_id` (optional): Classroom ID for filtering
- `start_date` (optional): Start date (YYYY-MM-DD format)
- `end_date` (optional): End date (YYYY-MM-DD format)

**Response Structure:**

```json
{
  "data": {
    "summary": {
      "total_records": 45,
      "positive_records": 30,
      "negative_records": 15,
      "total_points": 125,
      "positive_percentage": 66.67
    },
    "chart_data": {
      "labels": ["Positive Records", "Negative Records"],
      "datasets": [
        {
          "data": [30, 15],
          "backgroundColor": ["#2ecc71", "#e74c3c"]
        }
      ],
      "type": "doughnut"
    },
    "top_items": [
      {
        "item_title": "Good Behavior",
        "count": 12,
        "total_points": 60,
        "type": "PRS"
      }
    ],
    "monthly_trend": [
      {
        "month": "2024-01",
        "positive": 15,
        "negative": 8,
        "total_points": 65
      }
    ],
    "student_breakdown": [
      {
        "student_id": 123,
        "student_name": "John Doe",
        "total_records": 5,
        "positive_records": 3,
        "negative_records": 2,
        "total_points": 10
      }
    ]
  }
}
```

### 5. Homework Analytics Report

**Endpoint:** `GET /mobile-api/reports/staff/homework-analytics/`

**Parameters:**

- `authCode` (required): Authentication code
- `grade_id` (required): Grade/Class ID
- `start_date` (optional): Start date (YYYY-MM-DD format)
- `end_date` (optional): End date (YYYY-MM-DD format)

**Response Structure:**

```json
{
  "data": {
    "summary": {
      "total_homework_assigned": 20,
      "total_submissions": 450,
      "completed_submissions": 380,
      "completion_rate": 84.44
    },
    "chart_data": {
      "labels": ["Completed", "Pending"],
      "datasets": [
        {
          "data": [380, 70],
          "backgroundColor": ["#2ecc71", "#f39c12"]
        }
      ],
      "type": "doughnut"
    },
    "subject_breakdown": [
      {
        "subject_name": "Mathematics",
        "total_assignments": 5,
        "total_submissions": 125,
        "completed_submissions": 110,
        "completion_rate": 88.0
      }
    ],
    "daily_trend": [
      {
        "date": "2024-01-15",
        "assignments_due": 3,
        "submissions": 68,
        "completion_rate": 90.67
      }
    ]
  }
}
```

## Error Responses

All endpoints may return the following error responses:

### 400 Bad Request

```json
{
  "error": "Authentication code is required"
}
```

### 401 Unauthorized

```json
{
  "error": "Invalid authentication code"
}
```

### 403 Forbidden

```json
{
  "error": "Insufficient permissions for attendance reports"
}
```

### 404 Not Found

```json
{
  "error": "Student information not found"
}
```

### 500 Internal Server Error

```json
{
  "error": "Server error: [error message]"
}
```

## Permission Requirements

### Student Reports

- Students can only access their own reports
- No additional permissions required beyond valid authentication

### Staff Reports

- **Class Attendance Report**: Requires module 37 permission
- **Class Assessment Report**: Requires module 76 permission
- **Behavioral Analytics Report**: Requires module 56 permission
- **Homework Analytics Report**: No specific module permission required

## Role-Based Access

### Students

- Can access: attendance, grades, BPS, homework reports for themselves only

### Subject Teachers

- Can access: reports for classes they teach
- Limited to their assigned subjects and classes

### Homeroom Teachers

- Can access: reports for their homeroom class
- Full access to all report types for assigned students

### Head of Section

- Can access: reports for all classes in their section
- Full administrative access within their section

### Head of School

- Can access: all reports across the entire school
- Highest level of access for comprehensive analytics

## Chart Data Format

All chart data follows a consistent structure:

```json
{
    "labels": ["Label1", "Label2"],
    "datasets": [{
        "label": "Dataset Name",
        "data": [value1, value2],
        "backgroundColor": ["#color1", "#color2"]
    }],
    "type": "bar|line|doughnut|pie"
}
```

## Date Range Handling

- If no date range is provided, reports default to current month
- Date format: YYYY-MM-DD
- Both start_date and end_date must be provided together
- Invalid date ranges will fall back to default range

## Performance Considerations

- Reports are generated in real-time
- Large date ranges may take longer to process
- Consider implementing caching for frequently accessed reports
- Pagination may be needed for detailed breakdowns with large datasets

## Mobile App Integration

### Chart Rendering

- Chart data is optimized for mobile visualization libraries
- Supports Chart.js, D3.js, or native mobile chart components
- Color schemes are mobile-friendly with high contrast

### Data Caching

- Consider caching report data locally for offline viewing
- Implement refresh mechanisms for updated data
- Cache expiry should be based on data sensitivity

### User Experience

- Progress indicators recommended for report generation
- Error handling should provide user-friendly messages
- Support for export functionality (PDF, CSV) can be added

## Quick Reference

### Student Endpoints

```
GET /mobile-api/reports/available/?authCode={code}
GET /mobile-api/reports/student/attendance/?authCode={code}&start_date=2024-01-01&end_date=2024-01-31
GET /mobile-api/reports/student/grades/?authCode={code}&start_date=2024-01-01&end_date=2024-01-31
GET /mobile-api/reports/student/bps/?authCode={code}&start_date=2024-01-01&end_date=2024-01-31
GET /mobile-api/reports/student/homework/?authCode={code}&start_date=2024-01-01&end_date=2024-01-31
```

### Staff Endpoints

```
GET /mobile-api/reports/staff/classes/?authCode={code}
GET /mobile-api/reports/staff/class-attendance/?authCode={code}&classroom_id=10&start_date=2024-01-01&end_date=2024-01-31
GET /mobile-api/reports/staff/class-assessment/?authCode={code}&classroom_id=10&subject_id=5&start_date=2024-01-01&end_date=2024-01-31
GET /mobile-api/reports/staff/behavioral-analytics/?authCode={code}&classroom_id=10&start_date=2024-01-01&end_date=2024-01-31
GET /mobile-api/reports/staff/homework-analytics/?authCode={code}&grade_id=10&start_date=2024-01-01&end_date=2024-01-31
```

## Usage Examples

### Example 1: Get Student's Monthly Attendance Report

```javascript
const response = await fetch(
  '/mobile-api/reports/student/attendance/?authCode=abc123&start_date=2024-01-01&end_date=2024-01-31'
);
const data = await response.json();

if (data.success) {
  const attendanceRate = data.data.summary.attendance_rate;
  const chartData = data.data.chart_data;
  // Render chart and display statistics
}
```

### Example 2: Get Available Reports for User

```javascript
const response = await fetch('/mobile-api/reports/available/?authCode=abc123');
const data = await response.json();

if (data.success) {
  const reports = data.reports;
  // Display available reports in UI
  reports.forEach((report) => {
    console.log(`${report.name}: ${report.description}`);
  });
}
```

### Example 3: Get Class Assessment Report for Teacher

```javascript
const response = await fetch(
  '/mobile-api/reports/staff/class-assessment/?authCode=def456&classroom_id=10&start_date=2024-01-01&end_date=2024-01-31'
);
const data = await response.json();

if (data.success) {
  const classAverage = data.data.summary.class_average;
  const gradeDistribution = data.data.grade_distribution;
  // Display class performance metrics
}
```

## Implementation Notes

### Controller Structure

- All report endpoints are handled by `MobileReportsController`
- Helper class `MobileReportHelper` contains the analytics logic
- Authentication validation is centralized in the controller

### Database Queries

- Reports use optimized queries with proper indexing
- Date range filtering is applied at the database level
- Aggregations are performed efficiently using Laravel's query builder

### Security Considerations

- All endpoints require valid authentication
- Role-based access control is enforced
- Permission checks are performed for staff reports
- Students can only access their own data

### Testing Recommendations

- Test with different user roles (student, teacher, admin)
- Verify permission restrictions work correctly
- Test with various date ranges including edge cases
- Validate chart data structure and format
- Test error handling for invalid parameters

## Changelog

### Version 1.0

- Initial implementation of mobile reports API
- Support for student and staff reports
- Chart data optimization for mobile
- Role-based access control
- Comprehensive error handling
